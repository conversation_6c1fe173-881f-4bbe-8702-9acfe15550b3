import React, { useState } from 'react';
import { WebContext, WebInteractionResult } from '~/types';

type Props = { onBack: () => void };

function sendExecuteTool(name: string, args: Record<string, any> = {}) {
  return new Promise<WebInteractionResult<any>>((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(
        {
          name: 'execute-tool',
          body: { name, arguments: args },
        },
        (res: WebInteractionResult<any>) => {
          // Check for Chrome runtime errors
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome runtime error: ${chrome.runtime.lastError.message}`));
            return;
          }

          // Check if the response indicates an error
          if (res && !res.success && res.error) {
            resolve(res); // Let the calling code handle tool-level errors
            return;
          }

          resolve(res);
        }
      );
    } catch (error) {
      reject(
        new Error(
          `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      );
    }
  });
}

const TestWebToolkit: React.FC<Props> = ({ onBack }) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [result, setResult] = useState<WebInteractionResult<any> | null>(null);
  const [selectorOrIndex, setSelectorOrIndex] = useState<string>('0');
  const [copied, setCopied] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('hello');
  const [inputClearFirst, setInputClearFirst] = useState<boolean>(true);
  const [inputPressEnter, setInputPressEnter] = useState<boolean>(false);
  const [keys, setKeys] = useState<string>('Escape');
  const [refreshTimeout, setRefreshTimeout] = useState<number>(5000);
  const [analyzeSelector, setAnalyzeSelector] = useState<string>('');
  const [webContext, setWebContext] = useState<WebContext | null>(null);
  const [generateDiff, setGenerateDiff] = useState<boolean>(false);

  const run = async (tool: string, args: Record<string, any> = {}) => {
    try {
      setLoading(tool);
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Tool execution timeout (30 seconds)')), 10000);
      });

      args.context = webContext;
      const res = await Promise.race([sendExecuteTool(`WebToolkit_${tool}`, args), timeoutPromise]);
      if (res?.context) {
        setWebContext(res.context);
      }
      setResult(res ?? { success: false, error: 'no response' });
    } catch (error) {
      console.error('---[error] TestWebToolkit tool execution failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult({
        success: false,
        error: `Tool "${tool}" failed: ${errorMessage}`,
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <div style={{ height: '100vh', width: '100%', display: 'flex', flexDirection: 'column' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 12,
          borderBottom: '1px solid #eee',
        }}
      >
        <button onClick={onBack} style={{ marginRight: 12 }}>
          ← Back
        </button>
        <div style={{ fontWeight: 600 }}>Test WebToolkit</div>
      </div>

      <div style={{ padding: 12, display: 'grid', gap: 12 }}>
        {/* Analyze / Basic */}
        <div>
          <button
            onClick={() => run('extractText')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.summarize()"
          >
            {loading === 'extractText' ? 'Running…' : 'Extract Text'}
          </button>
        </div>

        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              value={analyzeSelector}
              onChange={e => setAnalyzeSelector(e.target.value)}
              placeholder="selector (optional)"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={generateDiff}
                onChange={e => setGenerateDiff(e.target.checked)}
              />
              Generate DOM Diff
            </label>
            <button
              onClick={() =>
                run('analyzePageDOM', {
                  selector: analyzeSelector || undefined,
                  generateDiff: generateDiff,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.buildDomTree()"
            >
              {loading === 'analyzePageDOM' ? 'Running…' : 'Analyze DOM'}
            </button>
          </div>
        </div>

        {/* TODO add to test getSimplifiedPageDomFunction and  */}

        <div>
          <button
            onClick={() => run('buildDomTemplate')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.buildDomTemplate()"
          >
            {loading === 'buildDomTemplate' ? 'Running…' : 'Build DOM Template'}
          </button>
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={selectorOrIndex}
            onChange={e => setSelectorOrIndex(e.target.value)}
            placeholder="selector or index"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('click', { selectorOrIndex })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.click(selectorOrIndex)"
          >
            {loading === 'click' ? 'Running…' : 'Click Element'}
          </button>
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={selectorOrIndex}
            onChange={e => setSelectorOrIndex(e.target.value)}
            placeholder="selector or index"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('scroll', { selectorOrIndex })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.scroll(selectorOrIndex)"
          >
            {loading === 'scroll' ? 'Running…' : 'Scroll To Element'}
          </button>
        </div>

        <div style={{ fontSize: 12, color: '#6b7280' }}>
          Tips: For index-based click, try 0, 1, 2 …; for selector-based click, try a CSS selector
          like #id or .class
        </div>

        {/* Input */}
        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              value={selectorOrIndex}
              onChange={e => setSelectorOrIndex(e.target.value)}
              placeholder="selector or index"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <input
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              placeholder="value"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <button
              onClick={() =>
                run('input', {
                  selectorOrIndex,
                  value: inputValue,
                  clearFirst: inputClearFirst,
                  pressEnterAfterInput: inputPressEnter,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.input(selectorOrIndex, value, options)"
            >
              {loading === 'input' ? 'Running…' : 'Input Value'}
            </button>
          </div>
          <div style={{ display: 'flex', gap: 16, alignItems: 'center', fontSize: 12 }}>
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={inputClearFirst}
                onChange={e => setInputClearFirst(e.target.checked)}
              />
              clearFirst
            </label>
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={inputPressEnter}
                onChange={e => setInputPressEnter(e.target.checked)}
              />
              pressEnterAfterInput
            </label>
          </div>
        </div>

        {/* Send Keys */}
        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={keys}
            onChange={e => setKeys(e.target.value)}
            placeholder="keys (e.g. Escape, Enter, Control+Shift+T)"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('sendKeys', { keys })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.sendKeys({ keys })"
          >
            {loading === 'sendKeys' ? 'Running…' : 'Send Keys'}
          </button>
        </div>

        {/* Screenshot */}
        <div>
          <button
            onClick={() => run('screenshot')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.screenshot()"
          >
            {loading === 'screenshot' ? 'Running…' : 'Screenshot'}
          </button>
        </div>

        {/* Refresh Page */}
        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              type="number"
              value={refreshTimeout}
              onChange={e => setRefreshTimeout(Number(e.target.value))}
              placeholder="timeout (ms)"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <button
              onClick={() =>
                run('refreshPage', {
                  timeout: refreshTimeout,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.refreshPage(url?, waitForLoad?, timeout?)"
            >
              {loading === 'refreshPage' ? 'Running…' : 'Refresh Page'}
            </button>
          </div>
        </div>

        <div
          style={{
            marginTop: 8,
            padding: 12,
            border: '1px solid #eee',
            borderRadius: 8,
            background: '#fafafa',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 6,
            }}
          >
            <div style={{ fontWeight: 600 }}>Result</div>
            <button
              onClick={async () => {
                try {
                  const text = JSON.stringify(result?.data ?? result, null, 2);
                  await navigator.clipboard.writeText(text ?? '');
                  setCopied(true);
                  setTimeout(() => setCopied(false), 1200);
                } catch {}
              }}
              title="Copy result"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: 6,
                padding: '4px 8px',
                borderRadius: 6,
                border: '1px solid #e5e7eb',
                background: '#ffffff',
                cursor: 'pointer',
                fontSize: 12,
                color: '#374151',
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
              {copied ? 'Copied' : 'Copy'}
            </button>
          </div>
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: 12 }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TestWebToolkit;
